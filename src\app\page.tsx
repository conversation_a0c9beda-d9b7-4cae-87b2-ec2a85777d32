'use client';

import React, { useState } from 'react';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/context/AuthContext';
import { useCategoriesQuery } from '@/hooks/queries/useCategoriesQuery';
import { useRecentLinksQuery } from '@/hooks/queries/useLinksQuery';
import LinkCard from '@/components/UI/LinkCard';
import TopLinksSection from '@/components/UI/TopLinksSection';
import {
  PlusIcon,
  ArrowRightIcon,
  BookOpenIcon,
  CodeBracketIcon,
  AcademicCapIcon,
  GlobeAltIcon
} from '@heroicons/react/24/outline';

export default function HomePage() {
  const { user } = useAuth();
  const router = useRouter();
  const [searchQuery, setSearchQuery] = useState('');

  // Use React Query hooks for data fetching
  const { data: categories = [], isLoading: categoriesLoading } = useCategoriesQuery();
  const {
    data: recentLinks = [],
    isLoading: linksLoading,
    error: linksError
  } = useRecentLinksQuery(6);

  const loading = categoriesLoading || linksLoading;
  const error = linksError?.message;

  // Category icons mapping
  const getCategoryIcon = (categoryName: string) => {
    const name = categoryName.toLowerCase();
    if (name.includes('code') || name.includes('programm')) return CodeBracketIcon;
    if (name.includes('learn') || name.includes('lern')) return AcademicCapIcon;
    if (name.includes('read') || name.includes('buch')) return BookOpenIcon;
    return GlobeAltIcon;
  };

  // Handle search
  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    if (searchQuery.trim()) {
      router.push(`/search?q=${encodeURIComponent(searchQuery.trim())}`);
      setSearchQuery('');
    }
  };

  return (
    <div className="min-h-screen">
      {/* Hero Section */}
      <section className="bg-gradient-to-br from-blue-50 to-indigo-100 py-16 sm:py-24">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <h1 className="text-4xl font-bold text-gray-900 sm:text-5xl md:text-6xl">
              Die besten Lernlinks –
              <span className="text-blue-600"> empfohlen von echten Menschen</span>
            </h1>
            <p className="mt-6 max-w-2xl mx-auto text-xl text-gray-600">
              Schnell zu den besten Lern-Links kommen – nicht über Algorithmen, sondern durch Menschen, die es ehrlich empfehlen.
            </p>

            {/* Prominenter Suchschlitz */}
            <div className="mt-10 max-w-2xl mx-auto">
              <form onSubmit={handleSearch} className="relative">
                <input
                  type="text"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  placeholder="Was möchtest du lernen?"
                  className="w-full px-6 py-4 text-lg border border-gray-300 rounded-full shadow-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500 pr-12 placeholder-gray-500"
                />
                <button
                  type="submit"
                  className="absolute right-2 top-1/2 transform -translate-y-1/2 p-2 text-gray-400 hover:text-blue-600 transition-colors"
                >
                  <svg className="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                  </svg>
                </button>
              </form>
            </div>
            <div className="mt-8 flex flex-col sm:flex-row justify-center gap-4">
              {user ? (
                <Link
                  href="/submit"
                  className="inline-flex items-center px-8 py-3 border border-transparent text-base font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 transition-colors"
                >
                  <PlusIcon className="h-5 w-5 mr-2" />
                  Link einreichen
                </Link>
              ) : (
                <Link
                  href="/register"
                  className="inline-flex items-center px-8 py-3 border border-transparent text-base font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 transition-colors"
                >
                  Jetzt starten
                </Link>
              )}
              <Link
                href="/categories"
                className="inline-flex items-center px-8 py-3 border border-gray-300 text-base font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 transition-colors"
              >
                Kategorien entdecken
                <ArrowRightIcon className="h-5 w-5 ml-2" />
              </Link>
            </div>
          </div>
        </div>
      </section>

      {/* Categories Section */}
      <section className="py-16 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900">Beliebte Kategorien</h2>
            <p className="mt-4 text-lg text-gray-600">
              Finde Links zu deinen Lieblingsthemen
            </p>
          </div>

          {categoriesLoading ? (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              {[...Array(8)].map((_, i) => (
                <div key={i} className="animate-pulse">
                  <div className="bg-gray-200 rounded-lg h-32"></div>
                </div>
              ))}
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              {categories.slice(0, 8).map((category) => {
                const IconComponent = getCategoryIcon(category.name);
                return (
                  <Link
                    key={category.id}
                    href={`/category/${category.slug}`}
                    className="group relative bg-white rounded-lg border border-gray-200 p-6 hover:border-blue-300 hover:shadow-lg transition-all duration-200"
                  >
                    <div className="flex items-center space-x-3 mb-3">
                      <div className="flex-shrink-0">
                        <IconComponent className="h-8 w-8 text-blue-600 group-hover:text-blue-700" />
                      </div>
                      <h3 className="text-lg font-semibold text-gray-900 group-hover:text-blue-600">
                        {category.name}
                      </h3>
                    </div>
                    {category.description && (
                      <p className="text-sm text-gray-600 mb-3">
                        {category.description}
                      </p>
                    )}
                    <div className="flex items-center justify-between">
                      <span className="text-sm text-gray-500">
                        {category.totalLinks || 0} Links
                      </span>
                      <ArrowRightIcon className="h-4 w-4 text-gray-400 group-hover:text-blue-600 group-hover:translate-x-1 transition-all" />
                    </div>
                  </Link>
                );
              })}
            </div>
          )}

          <div className="text-center mt-8">
            <Link
              href="/categories"
              className="inline-flex items-center text-blue-600 hover:text-blue-700 font-medium"
            >
              Alle Kategorien anzeigen
              <ArrowRightIcon className="h-4 w-4 ml-1" />
            </Link>
          </div>
        </div>
      </section>

      {/* Top Rated Links Section */}
      <TopLinksSection timeframe="week" limit={4} />

      {/* Recent Links Section */}
      <section className="py-16 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900">Neueste Links</h2>
            <p className="mt-4 text-lg text-gray-600">
              Frisch von der Community kuratiert
            </p>
          </div>

          {loading ? (
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {[...Array(6)].map((_, i) => (
                <div key={i} className="animate-pulse">
                  <div className="bg-white rounded-lg border border-gray-200 p-6">
                    <div className="h-4 bg-gray-200 rounded w-3/4 mb-4"></div>
                    <div className="h-3 bg-gray-200 rounded w-full mb-2"></div>
                    <div className="h-3 bg-gray-200 rounded w-2/3"></div>
                  </div>
                </div>
              ))}
            </div>
          ) : error ? (
            <div className="text-center py-12">
              <div className="text-red-500 mb-4">
                <svg className="h-16 w-16 mx-auto mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L4.268 18.5c-.77.833.192 2.5 1.732 2.5z" />
                </svg>
              </div>
              <h3 className="text-lg font-medium text-gray-900 mb-2">
                Fehler beim Laden
              </h3>
              <p className="text-gray-600 mb-6">{error}</p>
              <button
                onClick={() => window.location.reload()}
                className="inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 transition-colors"
              >
                Erneut versuchen
              </button>
            </div>
          ) : recentLinks.length > 0 ? (
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {recentLinks.slice(0, 6).map((link) => (
                <LinkCard
                  key={link.id}
                  link={link}
                  showCategory={true}
                />
              ))}
            </div>
          ) : (
            <div className="text-center py-12">
              <GlobeAltIcon className="h-16 w-16 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">
                Noch keine genehmigten Links vorhanden
              </h3>
              <p className="text-gray-600 mb-6">
                Die Community wächst noch! Sei der Erste und teile einen hochwertigen Link.
              </p>
              <div className="flex flex-col sm:flex-row justify-center gap-4">
                {user ? (
                  <>
                    <Link
                      href="/submit"
                      className="inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 transition-colors"
                    >
                      <PlusIcon className="h-5 w-5 mr-2" />
                      Link einreichen
                    </Link>
                    <Link
                      href="/categories"
                      className="inline-flex items-center px-6 py-3 border border-gray-300 text-base font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 transition-colors"
                    >
                      Kategorien entdecken
                    </Link>
                  </>
                ) : (
                  <>
                    <Link
                      href="/register"
                      className="inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 transition-colors"
                    >
                      Jetzt registrieren
                    </Link>
                    <Link
                      href="/categories"
                      className="inline-flex items-center px-6 py-3 border border-gray-300 text-base font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 transition-colors"
                    >
                      Kategorien ansehen
                    </Link>
                  </>
                )}
              </div>
            </div>
          )}
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-16 bg-blue-600">
        <div className="max-w-4xl mx-auto text-center px-4 sm:px-6 lg:px-8">
          <h2 className="text-3xl font-bold text-white mb-4">
            Bereit, Teil der Community zu werden?
          </h2>
          <p className="text-xl text-blue-100 mb-8">
            Teile deine Lieblings-Ressourcen und entdecke neue Schätze
          </p>
          {!user && (
            <Link
              href="/register"
              className="inline-flex items-center px-8 py-3 border border-transparent text-base font-medium rounded-md text-blue-600 bg-white hover:bg-gray-50 transition-colors"
            >
              Kostenlos registrieren
              <ArrowRightIcon className="h-5 w-5 ml-2" />
            </Link>
          )}
        </div>
      </section>
    </div>
  );
}
